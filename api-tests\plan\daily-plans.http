### 每日计划接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取用户token
< ../auth/user-auth.http

@apiUrl = http://localhost:3004/api
@testUserToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJvcGVuaWQiOiJvcGVuaWRfdGVzdF8xMjMiLCJpYXQiOjE3NTQ5MTUzMzIsImV4cCI6MTc1NzUwNzMzMn0.kkI_Fiw8KcrJETT8JTw5eDBLtUIj25EoAOeELtBwEj0
@testMeditationId = 1
@testPlanId = 1
@defaultPage = 1
@defaultLimit = 10

### 1. 获取每日计划 - 今日计划
GET {{apiUrl}}/plan/daily
Authorization: Bearer {{testUserToken}}

### 2. 获取每日计划 - 指定日期
GET {{apiUrl}}/plan/daily?date=2025-08-11
Authorization: Bearer {{testUserToken}}

### 3. 获取每日计划 - 包含详细信息
GET {{apiUrl}}/plan/daily?include_details=true
Authorization: Bearer {{testUserToken}}

### 4. 获取每日计划 - 包含进度信息
GET {{apiUrl}}/plan/daily?include_progress=true
Authorization: Bearer {{testUserToken}}

### 5. 获取每日计划 - 按状态筛选（未完成）
GET {{apiUrl}}/plan/daily?status=pending
Authorization: Bearer {{testUserToken}}

### 6. 获取每日计划 - 按状态筛选（已完成）
GET {{apiUrl}}/plan/daily?status=completed
Authorization: Bearer {{testUserToken}}

### 7. 获取每日计划 - 按优先级排序
GET {{apiUrl}}/plan/daily?sort=priority&order=desc
Authorization: Bearer {{testUserToken}}

### 8. 添加到计划 - 添加一次性冥想内容
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "once",
  "start_date": "2025-08-11",
  "start_time": "08:00"
}

### 9. 添加到计划 - 添加循环计划（每天，一周）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "week",
  "cycle_frequency": "daily",
  "start_date": "2025-08-11",
  "start_time": "08:00"
}

### 10. 添加到计划 - 添加循环计划（两天一次，一个月）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "month",
  "cycle_frequency": "every_two_days",
  "start_date": "2025-08-11",
  "start_time": "09:00"
}

### 11. 添加到计划 - 添加循环计划（每天，三个月）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "quarter",
  "cycle_frequency": "daily",
  "start_date": "2025-08-11",
  "start_time": "07:30"
}

### 12. 添加到计划 - 缺少必填字段（meditation_id）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "plan_type": "once",
  "start_date": "2025-08-11"
}

### 13. 添加到计划 - 循环计划缺少必填字段（cycle_duration）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_frequency": "daily",
  "start_date": "2025-08-11"
}

### 14. 添加到计划 - 循环计划缺少必填字段（cycle_frequency）
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "week",
  "start_date": "2025-08-11"
}

### 15. 添加到计划 - 无效的冥想ID
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": 99999,
  "plan_type": "once",
  "start_date": "2025-08-11"
}

### 16. 添加到计划 - 无效的计划类型
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "invalid_type",
  "start_date": "2025-08-11"
}

### 17. 添加到计划 - 无效的循环天数
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "invalid_duration",
  "cycle_frequency": "daily",
  "start_date": "2025-08-11"
}

### 18. 添加到计划 - 无效的循环频率
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "week",
  "cycle_frequency": "invalid_frequency",
  "start_date": "2025-08-11"
}

### 16. 从计划删除 - 正常删除
DELETE {{apiUrl}}/plan/item/{{testPlanId}}
Authorization: Bearer {{testUserToken}}

### 17. 从计划删除 - 不存在的计划项
DELETE {{apiUrl}}/plan/item/99999
Authorization: Bearer {{testUserToken}}

### 18. 从计划删除 - 无效的计划项ID
DELETE {{apiUrl}}/plan/item/invalid_id
Authorization: Bearer {{testUserToken}}

### 19. 完成计划项 - 正常完成
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "completed_at": "2024-01-15T08:30:00Z",
  "notes": "完成晨间冥想",
  "rating": 5
}

### 20. 完成计划项 - 最小信息完成
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{}

### 21. 完成计划项 - 包含详细反馈
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "completed_at": "2024-01-15T08:30:00Z",
  "notes": "感觉很放松，心情平静",
  "rating": 4,
  "duration_actual": 12,
  "mood_before": "stressed",
  "mood_after": "calm"
}

### 22. 完成计划项 - 不存在的计划项
PUT {{apiUrl}}/plan/item/99999/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "notes": "测试不存在的计划项"
}

### 23. 完成计划项 - 无效评分
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "rating": 10,
  "notes": "无效评分测试"
}

### 24. 完成计划项 - 负数评分
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "rating": -1,
  "notes": "负数评分测试"
}

### 25. 获取历史计划 - 默认查询
GET {{apiUrl}}/plan/history
Authorization: Bearer {{testUserToken}}

### 26. 获取历史计划 - 指定分页
GET {{apiUrl}}/plan/history?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{testUserToken}}

### 27. 获取历史计划 - 按日期范围筛选
GET {{apiUrl}}/plan/history?start_date=2024-01-01&end_date=2024-01-31&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 28. 获取历史计划 - 按状态筛选
GET {{apiUrl}}/plan/history?status=completed&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 29. 获取历史计划 - 按完成情况筛选
GET {{apiUrl}}/plan/history?completion_status=completed&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 30. 获取历史计划 - 按时间排序
GET {{apiUrl}}/plan/history?sort=completed_at&order=desc&page=1&limit=20
Authorization: Bearer {{testUserToken}}

### 31. 获取计划统计 - 基础统计
GET {{apiUrl}}/plan/stats
Authorization: Bearer {{testUserToken}}

### 32. 获取计划统计 - 按时间范围
GET {{apiUrl}}/plan/stats?start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer {{testUserToken}}

### 33. 获取计划统计 - 按天分组
GET {{apiUrl}}/plan/stats?group_by=day&days=7
Authorization: Bearer {{testUserToken}}

### 34. 获取计划统计 - 按周分组
GET {{apiUrl}}/plan/stats?group_by=week&weeks=4
Authorization: Bearer {{testUserToken}}

### 35. 获取计划统计 - 按月分组
GET {{apiUrl}}/plan/stats?group_by=month&months=6
Authorization: Bearer {{testUserToken}}

### 36. 获取计划统计 - 详细统计
GET {{apiUrl}}/plan/stats?include_details=true
Authorization: Bearer {{testUserToken}}

### 37. 权限测试 - 无token访问每日计划
GET {{apiUrl}}/plan/daily

### 38. 权限测试 - 无token添加计划
POST {{apiUrl}}/plan/add
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "priority": "medium"
}

### 39. 权限测试 - 无token删除计划
DELETE {{apiUrl}}/plan/item/{{testPlanId}}

### 40. 权限测试 - 无token完成计划
PUT {{apiUrl}}/plan/item/{{testPlanId}}/complete
Content-Type: application/json

{}

### 41. 权限测试 - 无效token访问
GET {{apiUrl}}/plan/daily
Authorization: Bearer invalid_token

### 42. 批量操作测试 - 连续添加多个计划
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": 1,
  "plan_type": "once",
  "start_date": "2024-01-16",
  "start_time": "08:00"
}

###
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": 2,
  "plan_type": "once",
  "start_date": "2024-01-16",
  "start_time": "09:00"
}

### 43. 性能测试 - 大量历史数据查询
GET {{apiUrl}}/plan/history?page=1&limit=100
Authorization: Bearer {{testUserToken}}

### 44. 性能测试 - 复杂统计查询
GET {{apiUrl}}/plan/stats?group_by=day&days=365&include_details=true
Authorization: Bearer {{testUserToken}}

### 45. 边界测试 - 未来很远的日期
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "once",
  "start_date": "2030-12-31",
  "start_time": "12:00"
}

### 46. 边界测试 - 长期循环计划
POST {{apiUrl}}/plan/add
Authorization: Bearer {{testUserToken}}
Content-Type: application/json

{
  "meditation_id": {{testMeditationId}},
  "plan_type": "recurring",
  "cycle_duration": "quarter",
  "cycle_frequency": "daily",
  "start_date": "2025-08-11",
  "start_time": "06:00"
}
